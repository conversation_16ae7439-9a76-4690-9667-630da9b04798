# Database

目前只支持RocksDB。

## 使用方法

### 1. 初始化数据库

在使用数据库之前，必须先调用 `init_db()` 函数来指定数据库文件目录：

```rust
use crate::database::db;

// 使用相对路径
db::init_db("./data/my_database").unwrap();

// 使用绝对路径
db::init_db("/home/<USER>/myapp/database").unwrap();

// 使用环境变量
let db_path = std::env::var("DATABASE_PATH").unwrap_or_else(|_| "./data/db".to_string());
db::init_db(&db_path).unwrap();
```

### 2. 获取数据库实例

初始化后，可以通过 `get_db()` 函数获取全局数据库实例：

```rust
let db = db::get_db();

// 写入数据
db.put(b"key1", b"value1").unwrap();

// 读取数据
match db.get(b"key1").unwrap() {
    Some(value) => println!("Found: {:?}", String::from_utf8_lossy(&value)),
    None => println!("Key not found"),
}
```

### 3. 检查初始化状态

可以使用 `is_initialized()` 函数检查数据库是否已初始化：

```rust
if db::is_initialized() {
    println!("Database is ready");
} else {
    println!("Database not initialized");
}
```

## 环境变量

可以通过以下环境变量配置数据库路径：

- `DB_PATH`: 数据库文件目录路径（默认: `./data/solquant_db`）

示例：
```bash
export DB_PATH="/var/lib/solquant/database"
./solquant
```

## 注意事项

1. **单次初始化**: 数据库只能初始化一次，重复调用 `init_db()` 会返回错误
2. **线程安全**: 数据库实例是线程安全的，可以在多个线程中安全使用
3. **目录创建**: 如果指定的目录不存在，RocksDB 会自动创建
4. **错误处理**: 初始化失败时会返回详细的错误信息
