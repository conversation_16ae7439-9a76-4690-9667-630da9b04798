use rocksdb::DB;
use rocksdb::{ColumnFamilyDescriptor, Options};
use std::path::Path;
use std::sync::{Arc, OnceLock};
static DB_INSTANCE: OnceLock<Arc<DB>> = OnceLock::new();

#[allow(unused)]
/// init_db: 初始化数据库，指定数据库文件目录
pub fn init_db<P: AsRef<Path>>(db_path: P) -> Result<(), String> {
    let db = init_db_impl(db_path)?;

    DB_INSTANCE
        .set(Arc::new(db))
        .map_err(|_| "Database already initialized".to_string())?;

    Ok(())
}

fn init_db_impl<P: AsRef<Path>>(db_path: P) -> Result<DB, String> {
    let db = DB::open_default(db_path.as_ref())
        .map_err(|e| format!("Failed to open database: {}", e))?;
    Ok(db)
}

/// 初始化数据库并创建指定的 column families
pub fn init_db_with_cfs<P: AsRef<Path>>(db_path: P, cf_names: &[&str]) -> Result<(), String> {
    let db = init_db_with_cfs_impl(db_path, cf_names)?;

    DB_INSTANCE
        .set(Arc::new(db))
        .map_err(|_| "Database already initialized".to_string())?;

    Ok(())
}

fn init_db_with_cfs_impl<P: AsRef<Path>>(db_path: P, cf_names: &[&str]) -> Result<DB, String> {
    let mut opts = Options::default();
    opts.create_if_missing(true);
    opts.create_missing_column_families(true);

    let mut cfs = vec![ColumnFamilyDescriptor::new("default", Options::default())];
    for cf_name in cf_names {
        cfs.push(ColumnFamilyDescriptor::new(*cf_name, Options::default()));
    }

    let db = DB::open_cf_descriptors(&opts, db_path.as_ref(), cfs)
        .map_err(|e| format!("Failed to open database with column families: {}", e))?;

    Ok(db)
}

/// 获取全局数据库实例
pub fn get_db() -> Arc<DB> {
    DB_INSTANCE
        .get()
        .expect("Database not initialized. Call init_db() first.")
        .clone()
}

#[allow(unused)]
/// 检查数据库是否已初始化
pub fn is_initialized() -> bool {
    DB_INSTANCE.get().is_some()
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    use tracing_subscriber;

    #[test]
    fn test_init_db_impl() {
        let _ = tracing_subscriber::fmt::try_init();
        let dir = tempdir().unwrap();
        let path = dir.path().join("test_init_db");
        assert!(init_db_impl(&path).is_ok());
    }

    #[test]
    fn test_init_db_with_cfs_impl() {
        let _ = tracing_subscriber::fmt::try_init();
        let dir = tempdir().unwrap();
        let path = dir.path().join("test_init_db_with_cfs");
        let db = init_db_with_cfs_impl(&path, &["test"]);
        assert!(db.is_ok());
        let db = db.unwrap();
        assert!(db.cf_handle("test").is_some());
        assert!(db.cf_handle("nonexistent").is_none());
    }

    #[test]
    fn test_db_read_write() {
        let _ = tracing_subscriber::fmt::try_init();
        let dir = tempdir().unwrap();
        let path = dir.path().join("test_db_read_write");
        let db = init_db_with_cfs_impl(&path, &["test"]);
        assert!(db.is_ok());
        let db = db.unwrap();
        assert!(db.put(b"key", b"value").is_ok());
        assert_eq!(db.get(b"key").unwrap().unwrap(), b"value");
        let cf = db.cf_handle("test").unwrap();
        assert!(db.put_cf(cf, b"key", b"value").is_ok());
        assert_eq!(db.get_cf(cf, b"key").unwrap().unwrap(), b"value");
        let result = db.get_cf(cf, b"other");
        assert!(result.is_ok());
        let value = result.unwrap();
        assert!(value.is_none());
    }

    #[test]
    fn test_db_iterator() {
        let _ = tracing_subscriber::fmt::try_init();
        let dir = tempdir().unwrap();
        let path = dir.path().join("test_db_iterator");
        let db = init_db_with_cfs_impl(&path, &["test"]);
        assert!(db.is_ok());
        let db = db.unwrap();
        let mut wb = rocksdb::WriteBatch::default();
        let cf = db.cf_handle("test").unwrap();
        wb.put_cf(cf, b"a1", b"value1");
        wb.put_cf(cf, b"a2", b"value2");
        wb.put_cf(cf, b"b1", b"value3");
        db.write(wb).unwrap();
        let mut iter = db.iterator_cf(
            cf,
            rocksdb::IteratorMode::From(b"b", rocksdb::Direction::Forward),
        );
        assert_eq!(iter.next().unwrap().unwrap().0.as_ref(), b"b1");
        assert!(iter.next().is_none());
    }
}
