use crate::common::config;
use std::collections::HashMap;
use std::time::Duration;

pub async fn get_sol_price_in_usd() -> Result<f64, Box<dyn std::error::Error>> {
    get_pair("solana", "usd").await
}

// Returns the value of 1 a in terms of b
async fn get_pair(a: &str, b: &str) -> Result<f64, Box<dyn std::error::Error>> {
    let timeout = Duration::from_millis(config::get().timeout_ms);
    let client = reqwest::Client::builder().timeout(timeout).build()?;

    let url = format!(
        "https://api.coingecko.com/api/v3/simple/price?ids={}&vs_currencies={}",
        a, b
    );
    let response = client
        .get(&url)
        .send()
        .await?
        .error_for_status()?
        .json::<HashMap<String, HashMap<String, f64>>>()
        .await?;

    let price = response
        .get(a)
        .and_then(|prices| prices.get(b))
        .ok_or("Failed to parse price from CoinGecko response")?;

    Ok(*price)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::test;

    #[test]
    async fn test_get_sol_price_in_usd() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = get_sol_price_in_usd().await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result > 0.0);
    }
}
