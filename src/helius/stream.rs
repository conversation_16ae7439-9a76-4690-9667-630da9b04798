use futures_util::{SinkExt, StreamExt};
use serde_json::Value;
use std::error::Error;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};
use tracing::{info, warn};

use crate::common::config;

macro_rules! create_subscribe_account_request {
    ($account:expr) => {
        serde_json::json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "accountSubscribe",
            "params": [
                $account,
                {
                    "commitment": "confirmed",
                    "encoding": "jsonParsed",
                    "transactionDetails": "full",
                    "maxSupportedTransactionVersion": 0
                }
            ]
        })
    };
}

/// subscribe_account allow user provide a function which can receive the Result<Message, Error> and deal with it
#[allow(unused)]
pub async fn subscribe_account<F, Fut>(account: &str, f: F) -> Result<(), Box<dyn Error>>
where
    F: Fn(Result<Message, tungstenite::error::Error>) -> Fut,
    Fut: Future<Output = Result<(), Box<dyn Error>>> + Send + 'static,
{
    let request = create_subscribe_account_request!(account);
    subscribe(request, f).await
}

#[allow(unused)]
/// subscribe_account_msg allow user provide a function which can receive a value and deal the value
pub async fn subscribe_account_msg<F, Fut>(account: &str, f: F) -> Result<(), Box<dyn Error>>
where
    F: Fn(Value) -> Fut,
    Fut: Future<Output = Result<(), Box<dyn Error>>> + Send + 'static,
{
    let request = create_subscribe_account_request!(account);
    subscribe_msg(request, f).await
}

async fn subscribe<F, Fut>(request: Value, f: F) -> Result<(), Box<dyn Error>>
where
    F: Fn(Result<Message, tungstenite::error::Error>) -> Fut,
    Fut: Future<Output = Result<(), Box<dyn Error>>> + Send + 'static,
{
    let ws_url = config::get().get_ws_url();
    let (ws_stream, _) = connect_async(ws_url).await?;
    let (mut write, mut read) = ws_stream.split();

    write
        .send(Message::Text(request.to_string().into()))
        .await?;

    while let Some(msg) = read.next().await {
        f(msg).await?;
    }
    Ok(())
}

async fn subscribe_msg<F, Fut>(request: Value, f: F) -> Result<(), Box<dyn Error>>
where
    F: Fn(Value) -> Fut,
    Fut: Future<Output = Result<(), Box<dyn Error>>> + Send + 'static,
{
    let ws_url = config::get().get_ws_url();
    let (ws_stream, _) = connect_async(ws_url).await?;
    let (mut write, mut read) = ws_stream.split();

    write
        .send(Message::Text(request.to_string().into()))
        .await?;

    while let Some(msg) = read.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                if let Ok(data) = serde_json::from_str::<Value>(&text) {
                    f(data).await?;
                }
            }
            Ok(Message::Close(_)) => {
                info!("WebSocket connection closed");
                return Err("Connection closed".into());
            }
            Err(e) => {
                warn!("WebSocket error: {}", e);
                return Err(e.into());
            }
            _ => {}
        }
    }
    Ok(())
}
