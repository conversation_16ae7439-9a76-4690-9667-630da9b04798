use once_cell::sync::OnceCell;
use serde::Deserialize;
use std::fs;

static GLOBAL_CONFIG: OnceCell<Config> = OnceCell::new();

#[derive(Debug, Deserialize, Clone, PartialEq)]
pub enum Mode {
    DEV,
    PRODUCT,
}

#[derive(Debug, Deserialize, Clone)]
pub struct HeliusConfig {
    api_key: String,
}

impl HeliusConfig {
    pub fn get_rpc_url(&self, mode: &Mode) -> String {
        match mode {
            Mode::DEV => format!("https://devnet.helius-rpc.com/?api-key={}", self.api_key),
            Mode::PRODUCT => format!("https://mainnet.helius-rpc.com/?api-key={}", self.api_key),
        }
    }

    #[allow(dead_code)]
    fn get_ws_url(&self, mode: &Mode) -> String {
        match mode {
            Mode::DEV => format!("wss://devnet.helius-rpc.com/?api-key={}", self.api_key),
            Mode::PRODUCT => format!("wss://mainnet.helius-rpc.com/?api-key={}", self.api_key),
        }
    }
}

fn default_timeout_ms() -> u64 {
    30000
}

fn default_max_retry() -> u32 {
    3
}

fn default_db_path() -> String {
    ".solquant_db".to_string()
}

#[derive(Debug, Deserialize)]
pub struct Config {
    pub mode: Mode,
    #[serde(default = "default_timeout_ms")]
    pub timeout_ms: u64,
    #[serde(default = "default_max_retry")]
    pub max_retry: u32,
    #[serde(default = "default_db_path")]
    pub db_path: String,
    pub helius: HeliusConfig,
}

impl Config {
    pub fn get_rpc_url(&self) -> String {
        self.helius.get_rpc_url(&self.mode)
    }

    #[allow(dead_code)]
    pub fn get_api_key(&self) -> Option<&str> {
        Some(&self.helius.api_key)
    }

    #[allow(dead_code)]
    pub fn get_ws_url(&self) -> String {
        self.helius.get_ws_url(&self.mode)
    }

    pub fn get_raydium_url(&self) -> String {
        match self.mode {
            Mode::DEV => format!("https://api-v3-devnet.raydium.io"),
            Mode::PRODUCT => format!("https://api-v3.raydium.io"),
        }
    }
}

pub fn load_config(path: &str) -> Result<&'static Config, Box<dyn std::error::Error>> {
    let content = fs::read_to_string(path)?;
    let config: Config = toml::from_str(&content)?;
    GLOBAL_CONFIG.get_or_init(|| config);
    Ok(get())
}

pub fn get() -> &'static Config {
    GLOBAL_CONFIG.get_or_init(|| {
        let content = fs::read_to_string(".config.toml").unwrap();
        let config: Config = toml::from_str(&content).unwrap();
        config
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_load_config() {
        let _ = tracing_subscriber::fmt::try_init();

        let content = fs::read_to_string("src/common/template.toml").unwrap();
        let config: Config = toml::from_str(&content).unwrap();

        assert_eq!(config.mode, Mode::PRODUCT);
        assert_eq!(config.timeout_ms, 5000);
        assert_eq!(config.max_retry, 3);
        assert_eq!(
            config.helius.api_key,
            "you should not commit your api key here"
        );
    }
}
