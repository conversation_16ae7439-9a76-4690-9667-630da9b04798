use crate::database::common::get_db;
use crate::raydium::pool::*;
use rocksdb::WriteBatch;
use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
pub const CF_NAME: &str = "strategy.raydium";

const USDC_ADDRESS: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
const USDT_ADDRESS: &str = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
const SOL_ADDRESS: &str = "So11111111111111111111111111111111111111112";

#[allow(unused)]
pub async fn update_database() -> Result<(), Box<dyn Error>> {
    update_base_token_data(USDC_ADDRESS.to_string()).await?;
    update_base_token_data(USDT_ADDRESS.to_string()).await?;
    update_base_token_data(SOL_ADDRESS.to_string()).await?;
    Ok(())
}

macro_rules! update_pool_and_pair_info {
    ($db:expr, $cf:expr, $batch:expr, $pool:expr, $mint_a:expr, $mint_b:expr) => {
        // add pool info
        let pool_key = get_pool_key(&$pool.id);
        if $db
            .get_cf($cf, &pool_key)
            .map_err(|e| e.to_string())?
            .is_none()
        {
            $batch.put_cf(
                $cf,
                &pool_key,
                serde_json::to_string($pool)
                    .map_err(|e| e.to_string())?
                    .as_bytes(),
            );
        }

        // add pair info
        let pair_key = get_pair_key(&$mint_a.address, &$mint_b.address);
        if $db
            .get_cf($cf, &pair_key)
            .map_err(|e| e.to_string())?
            .is_none()
        {
            $batch.put_cf($cf, &pair_key, $pool.id.as_bytes());
        }

        let pair_key = get_pair_key(&$mint_b.address, &$mint_a.address);
        if $db
            .get_cf($cf, &pair_key)
            .map_err(|e| e.to_string())?
            .is_none()
        {
            $batch.put_cf($cf, &pair_key, $pool.id.as_bytes());
        }
    };
}

#[allow(unused)]
async fn update_base_token_data(token: String) -> Result<(), String> {
    let result = get_info_by_mint(
        token.clone(),
        None,
        PoolType::All,
        PoolSortField::Liquidity,
        SortType::Desc,
        10,
        1,
    )
    .await?;
    let db = get_db();
    let mut new_tokens = vec![];
    let cf = db
        .cf_handle(CF_NAME)
        .ok_or(format!("Column family {} not found", CF_NAME))?;

    let mut batch = WriteBatch::default();

    for pool in result.data.data {
        let (mint_token, mint_other) = if pool.mint_a.address == token {
            (&pool.mint_a, &pool.mint_b)
        } else {
            (&pool.mint_b, &pool.mint_a)
        };
        let mint_token_key = get_token_key(&mint_token.address);
        let mint_other_key = get_token_key(&mint_other.address);

        // add mint token info
        if db
            .get_cf(cf, &mint_token_key)
            .map_err(|e| e.to_string())?
            .is_none()
        {
            batch.put_cf(
                cf,
                &mint_token_key,
                serde_json::to_string(mint_token)
                    .map_err(|e| e.to_string())?
                    .as_bytes(),
            );
        }
        // add other token info
        if db
            .get_cf(cf, &mint_other_key)
            .map_err(|e| e.to_string())?
            .is_none()
        {
            batch.put_cf(
                cf,
                &mint_other_key,
                serde_json::to_string(mint_other)
                    .map_err(|e| e.to_string())?
                    .as_bytes(),
            );
            new_tokens.push(mint_other.address.clone());
        }
        tracing::info!(
            "Add token pair({}:{})",
            mint_token.symbol,
            mint_other.symbol
        );
        update_pool_and_pair_info!(db, cf, batch, &pool, mint_token, mint_other);
    }

    let max_concurrency = 8;
    let mut semaphore = Arc::new(tokio::sync::Semaphore::new(max_concurrency));
    let mut handles = vec![];
    for i in 0..new_tokens.len() {
        for j in i + 1..new_tokens.len() {
            let token1 = new_tokens[i].clone();
            let token2 = new_tokens[j].clone();
            let semaphore = semaphore.clone();
            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.map_err(|e| e.to_string())?;
                get_info_by_mint(
                    token1,
                    Some(token2),
                    PoolType::All,
                    PoolSortField::Liquidity,
                    SortType::Desc,
                    1,
                    1,
                )
                .await
            });
            handles.push(handle);
        }
    }
    for handle in handles {
        let result = handle.await.map_err(|e| e.to_string())??;
        if result.data.count == 1 {
            let pool = &result.data.data[0];
            tracing::info!(
                "Add token pair({}:{})",
                pool.mint_a.symbol,
                pool.mint_b.symbol
            );
            update_pool_and_pair_info!(db, cf, batch, &pool, &pool.mint_a, &pool.mint_b);
        }
    }

    db.write(batch)?;
    Ok(())
}

// value will be TokenInfo
fn get_token_key(address: &str) -> String {
    format!("token/{}", address)
}

// value will be PoolInfo
fn get_pool_key(id: &str) -> String {
    format!("pool/{}", id)
}

// value will be pool_id
fn get_pair_key(address1: &str, address2: &str) -> String {
    format!("pair/{}:{}", address1, address2)
}

#[allow(unused)]
// run_triangular_arbitrage_task will run in background
pub async fn run_triangular_arbitrage_task() {
    // 1. will update database first, but i think it is not necessary for now.
    // It should not run if database is not empty.
    // update_database().await.unwrap();
    // 2. find token pairs that should be spied
    let base_token = SOL_ADDRESS;
    let lps = find_pairs(base_token.to_string()).await;
    tracing::info!("lps are {:#?}", lps);
    loop {
        // 3. for each pair, we need to check if there is any opportunity
        match get_info_by_lps(lps.clone()).await {
            Ok(res) => {
                let pools = res.data;
                let mut prices = HashMap::new();
                let mut tokens = HashMap::new();
                let base_token = base_token.to_string();
                for pool in pools {
                    let mint_a = pool.mint_a.address;
                    let mint_b = pool.mint_b.address;
                    let price = pool.price;
                    let reverse_price = 1.0 / price;
                    prices.insert(format!("{}:{}", mint_a, mint_b), price);
                    prices.insert(format!("{}:{}", mint_b, mint_a), reverse_price);
                    if mint_a != base_token {
                        tokens.insert(mint_a.clone(), true);
                    }
                    if mint_b != base_token {
                        tokens.insert(mint_b.clone(), true);
                    }
                }
                let tokens: Vec<String> = tokens.keys().cloned().collect();
                for i in 0..tokens.len() {
                    for j in i + 1..tokens.len() {
                        match can_triangular_arbitrage(
                            &prices,
                            base_token.as_str(),
                            tokens[i].as_str(),
                            tokens[j].as_str(),
                        ) {
                            Some(profit) => {
                                if profit > 0.0 {
                                    tracing::info!(
                                        "Found triangular arbitrage: {} -> {} -> {} -> {} with profit {}%",
                                        base_token,
                                        tokens[i],
                                        tokens[j],
                                        base_token,
                                        profit * 100.0
                                    );
                                } else if profit < 0.0 {
                                    tracing::info!(
                                        "Found triangular arbitrage: {} -> {} -> {} -> {} with profit {}%",
                                        base_token,
                                        tokens[j],
                                        tokens[i],
                                        base_token,
                                        -profit * 100.0
                                    );
                                }
                            }
                            None => {
                                tracing::warn!(
                                    "you should deal with that case to avoid missing opportunits"
                                );
                            }
                        }
                    }
                }
            }
            Err(e) => {
                tracing::error!("Error: {}", e);
            }
        }

        tokio::time::sleep(std::time::Duration::from_secs(5)).await;
    }
}

// find_pairs will return pools that need to be spied
async fn find_pairs(base_token: String) -> Vec<String> {
    let db = get_db();
    let cf = db
        .cf_handle(CF_NAME)
        .ok_or(format!("Column family {} not found", CF_NAME))
        .unwrap();
    let mut pools = HashMap::new();
    let mut prices = HashMap::new();
    let mut tokens = HashMap::new();
    for kv in db.iterator_cf(
        cf,
        rocksdb::IteratorMode::From(b"pool", rocksdb::Direction::Forward),
    ) {
        let (key, value) = kv.unwrap();
        if key.starts_with(b"pool") {
            let pool: Pool = serde_json::from_slice(&value).unwrap();
            // ignore pool without lp mint for now to simplify the code
            if pool.lp_mint.is_none() {
                continue;
            }
            let mint_a = pool.mint_a.address;
            let mint_b = pool.mint_b.address;
            if mint_a != base_token {
                tokens.insert(mint_a.clone(), true);
            }
            if mint_b != base_token {
                tokens.insert(mint_b.clone(), true);
            }
            let lp_mint = pool.lp_mint.unwrap();
            let lp_id = lp_mint.address;
            let price = pool.price;
            let reverse_price = 1.0 / price;
            pools.insert(format!("{}:{}", mint_a, mint_b), lp_id.clone());
            pools.insert(format!("{}:{}", mint_b, mint_a), lp_id);
            prices.insert(format!("{}:{}", mint_a, mint_b), price);
            prices.insert(format!("{}:{}", mint_b, mint_a), reverse_price);
        } else {
            break;
        }
    }
    let mut lps = vec![];
    let tokens: Vec<String> = tokens.keys().cloned().collect();
    for i in 0..tokens.len() {
        for j in i + 1..tokens.len() {
            if let Some(_) = can_triangular_arbitrage(
                &prices,
                base_token.as_str(),
                tokens[i].as_str(),
                tokens[j].as_str(),
            ) {
                let mint_id = pools.get(&format!("{}:{}", tokens[i], tokens[j])).unwrap();
                lps.push(mint_id.clone());
            }
        }
    }
    // then add token and base token pair, but token to base_token may be not lp
    for token in tokens {
        if let Some(mint_id) = pools.get(&format!("{}:{}", token, base_token)) {
            lps.push(mint_id.clone());
        }
    }
    lps
}

// token_a should be base token that we want to earn
// price is token_1 / token_2, for example:
// if 1 sol == 180 usdc, then the key will be {sol}:{usdc}, and the value will be 180
// 1a = xb, so a/b = x, which means x = price[{token_a}:{token_b}]
// 1b = yc, so b/c = y, which means y = price[{token_b}:{token_c}]
// 1c = za, so c/a = z, which means z = price[{token_c}:{token_a}]
// if xyz == 1, return Some(0)
// if xyz > 1, return Some(xyz - 1), a->b->c->a
// if xyz < 1, return Some(1/xyz - 1), a->c->b->a
// if x, y or z is not exist, return None
fn can_triangular_arbitrage(
    prices: &HashMap<String, f64>,
    token_a: &str,
    token_b: &str,
    token_c: &str,
) -> Option<f64> {
    let x = *prices.get(&format!("{}:{}", token_a, token_b))?;
    let y = *prices.get(&format!("{}:{}", token_b, token_c))?;
    let z = *prices.get(&format!("{}:{}", token_c, token_a))?;
    let xyz = x * y * z;
    tracing::info!("{} * {} * {} = {}", x, y, z, xyz);
    if xyz > 1.0 {
        return Some(xyz - 1.0);
    } else if xyz < 1.0 {
        return Some(1.0 - 1.0 / xyz);
    }
    None
}
