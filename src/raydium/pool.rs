use crate::raydium::util::{get_url, send_get_request};
use serde::Deserialize;
use serde::Serialize;
use std::collections::HashMap;

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct InfoResponse {
    pub id: String,
    pub success: bool,
    pub data: Vec<PoolInfo>,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PoolInfo {
    #[serde(rename = "type")]
    pub pool_type: String,
    pub program_id: String,
    pub id: String,
    pub mint_a: TokenInfo,
    pub mint_b: TokenInfo,
    pub price: f64,
    pub mint_amount_a: f64,
    pub mint_amount_b: f64,
    pub fee_rate: f64,
    pub open_time: String,
    pub tvl: f64,
    pub day: TimeFrameStats,
    pub week: TimeFrameStats,
    pub month: TimeFrameStats,
    pub pooltype: Vec<String>,
    #[serde(default)]
    pub reward_default_pool_infos: Option<String>,
    pub reward_default_infos: Vec<RewardInfo>,
    pub farm_upcoming_count: u32,
    pub farm_ongoing_count: u32,
    pub farm_finished_count: u32,
    #[serde(default)]
    pub market_id: Option<String>,
    #[serde(default)]
    pub lp_mint: Option<TokenInfo>,
    #[serde(default)]
    pub lp_price: Option<f64>,
    #[serde(default)]
    pub lp_amount: Option<f64>,
    pub burn_percent: f64,
    pub launch_migrate_pool: bool,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct TokenInfo {
    pub chain_id: u32,
    pub address: String,
    pub program_id: String,
    #[serde(rename = "logoURI")]
    pub logo_uri: String,
    pub symbol: String,
    pub name: String,
    pub decimals: u8,
    pub tags: Vec<String>,

    #[serde(default)]
    pub extensions: HashMap<String, serde_json::Value>,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct TimeFrameStats {
    pub volume: f64,
    pub volume_quote: f64,
    pub volume_fee: f64,
    pub apr: f64,
    pub fee_apr: f64,
    pub price_min: f64,
    pub price_max: f64,
    pub reward_apr: Vec<f64>,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
pub struct RewardInfo {
    pub mint: TokenInfo,
    #[serde(rename = "perSecond")]
    pub per_second: String,
    #[serde(rename = "startTime", default)]
    pub start_time: Option<String>,
    #[serde(rename = "endTime", default)]
    pub end_time: Option<String>,
}

#[allow(unused)]
/// examples: ["58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2"]
pub async fn get_info_by_ids(ids: Vec<String>) -> Result<InfoResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!(
        "{}/pools/info/ids?ids={}",
        get_url(),
        ids.join(",")
    ))
    .await?
    .json()
    .await?;

    Ok(response)
}

#[allow(unused)]
/// examples: ["8HoQnePLqPj4M7PUDzfw8e3Ymdwgc7NLGnaTUapubyvu"]
pub async fn get_info_by_lps(lps: Vec<String>) -> Result<InfoResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!(
        "{}/pools/info/lps?lps={}",
        get_url(),
        lps.join(",")
    ))
    .await?
    .json()
    .await?;

    Ok(response)
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct ListInfoResponse {
    pub id: String,
    pub success: bool,
    pub data: PoolListData,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PoolListData {
    pub count: u32,
    pub data: Vec<ConcentratedPool>,
    pub has_next_page: bool,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct ConcentratedPool {
    #[serde(rename = "type")]
    pub pool_type: String,
    pub program_id: String,
    pub id: String,
    pub mint_a: TokenInfo,
    pub mint_b: TokenInfo,
    #[serde(default)]
    pub reward_default_pool_infos: Option<String>,
    pub reward_default_infos: Vec<RewardInfo>,
    pub price: f64,
    pub mint_amount_a: f64,
    pub mint_amount_b: f64,
    pub fee_rate: f64,
    pub open_time: String,
    pub tvl: f64,
    pub day: TimeFrameStats,
    pub week: TimeFrameStats,
    pub month: TimeFrameStats,
    pub pooltype: Vec<String>,
    pub farm_upcoming_count: u32,
    pub farm_ongoing_count: u32,
    pub farm_finished_count: u32,
    #[serde(default)]
    pub config: Option<PoolConfig>,
    #[serde(default)]
    pub market_id: Option<String>,
    #[serde(default)]
    pub lp_mint: Option<TokenInfo>,
    #[serde(default)]
    pub lp_price: Option<f64>,
    #[serde(default)]
    pub lp_amount: Option<f64>,
    pub burn_percent: f64,
    pub launch_migrate_pool: bool,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
pub struct PoolConfig {
    pub id: String,
    pub index: u32,
    #[serde(rename = "protocolFeeRate")]
    pub protocol_fee_rate: u32,
    #[serde(rename = "tradeFeeRate")]
    pub trade_fee_rate: u32,
    #[serde(rename = "tickSpacing", default)]
    pub tick_spacing: Option<u32>,
    #[serde(rename = "fundFeeRate", default)]
    pub fund_fee_rate: Option<u32>,
    #[serde(rename = "defaultRange", default)]
    pub default_range: Option<f64>,
    #[serde(rename = "defaultRangePoint", default)]
    pub default_range_point: Option<Vec<f64>>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Copy)]
#[serde(rename_all = "camelCase")]
pub enum PoolType {
    All,
    /// 标准流动性池
    Standard,
    /// 集中流动性池 (CLMM)
    Concentrated,
    /// 标准农场池
    StandardFarm,
    /// 集中流动性农场池
    ConcentratedFarm,
    /// 所有农场池
    AllFarm,
}

impl Default for PoolType {
    fn default() -> Self {
        PoolType::All // 指定All为默认值
    }
}

impl PoolType {
    /// 获取API查询参数值
    pub fn as_query_param(&self) -> &'static str {
        match self {
            PoolType::All => "all",
            PoolType::Standard => "standard",
            PoolType::Concentrated => "concentrated",
            PoolType::StandardFarm => "standardFarm",
            PoolType::ConcentratedFarm => "concentratedFarm",
            PoolType::AllFarm => "allFarm",
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, Copy)]
#[serde(rename_all = "camelCase")]
pub enum PoolSortField {
    /// 按流动性排序 (默认)
    Liquidity,
    /// 按24小时交易量排序
    Volume24h,
    /// 按24小时手续费排序
    Fee24h,
    /// 按24小时APR排序
    Apr24h,
    /// 按7天交易量排序
    Volume7d,
    /// 按7天手续费排序
    Fee7d,
    /// 按7天APR排序
    Apr7d,
    /// 按30天交易量排序
    Volume30d,
    /// 按30天手续费排序
    Fee30d,
    /// 按30天APR排序
    Apr30d,
}

impl Default for PoolSortField {
    fn default() -> Self {
        PoolSortField::Liquidity // 指定Liquidity为默认值
    }
}

impl PoolSortField {
    /// 获取API查询参数值
    pub fn as_query_param(&self) -> &'static str {
        match self {
            PoolSortField::Liquidity => "liquidity",
            PoolSortField::Volume24h => "volume24h",
            PoolSortField::Fee24h => "fee24h",
            PoolSortField::Apr24h => "apr24h",
            PoolSortField::Volume7d => "volume7d",
            PoolSortField::Fee7d => "fee7d",
            PoolSortField::Apr7d => "apr7d",
            PoolSortField::Volume30d => "volume30d",
            PoolSortField::Fee30d => "fee30d",
            PoolSortField::Apr30d => "apr30d",
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone, Copy)]
pub enum SortType {
    /// 升序排序
    Asc,
    /// 降序排序
    Desc,
}

impl SortType {
    /// 获取API查询参数值
    pub fn as_query_param(&self) -> &'static str {
        match self {
            SortType::Asc => "asc",
            SortType::Desc => "desc",
        }
    }
}

#[allow(unused)]
/// page_size should be smaller than 1000
pub async fn list_info(
    pool_type: PoolType,
    pool_sort_field: PoolSortField,
    sort_type: SortType,
    page_size: u64,
    page: u64,
) -> Result<ListInfoResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!(
        "{}/pools/info/list?poolType={}&poolSortField={}&sortType={}&pageSize={}&page={}",
        get_url(),
        pool_type.as_query_param(),
        pool_sort_field.as_query_param(),
        sort_type.as_query_param(),
        page_size,
        page,
    ))
    .await?
    .json()
    .await?;

    Ok(response)
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct MintInfoResponse {
    pub id: String,
    pub success: bool,
    pub data: PoolListData,
}

#[allow(unused)]
/// page_size should be smaller than 1000
/// examples: ["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"]
pub async fn get_info_by_mint(
    mint1: String,
    mint2: Option<String>,
    pool_type: PoolType,
    pool_sort_field: PoolSortField,
    sort_type: SortType,
    page_size: u64,
    page: u64,
) -> Result<MintInfoResponse, String> {
    let mint_param = if let Some(mint2) = mint2 {
        format!("mint1={}&mint2={}", mint1, mint2)
    } else {
        format!("mint1={}", mint1)
    };
    let response = send_get_request(format!(
        "{}/pools/info/mint?{}&poolType={}&poolSortField={}&sortType={}&pageSize={}&page={}",
        get_url(),
        mint_param,
        pool_type.as_query_param(),
        pool_sort_field.as_query_param(),
        sort_type.as_query_param(),
        page_size,
        page,
    ))
    .await
    .map_err(|e| e.to_string())?
    .json()
    .await
    .map_err(|e| e.to_string())?;

    Ok(response)
}

#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PoolDetailResponse {
    pub id: String,
    pub success: bool,
    pub data: Vec<PoolDetail>,
}

#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct PoolDetail {
    pub program_id: String,
    pub id: String, // Pool key
    pub mint_a: TokenInfo,
    pub mint_b: TokenInfo,
    #[serde(default)]
    pub lookup_table_account: Option<String>,
    pub open_time: String,
    pub vault: VaultAccounts,
    pub config: PoolConfig,
    #[serde(default)]
    pub reward_infos: Vec<RewardInfoDetail>,
    #[serde(default)]
    pub observation_id: Option<String>,
    #[serde(default)]
    pub ex_bitmap_account: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct VaultAccounts {
    #[serde(rename = "A")]
    pub a: String, // Token A vault account
    #[serde(rename = "B")]
    pub b: String, // Token B vault account
}

#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct RewardInfoDetail {
    pub mint: TokenInfo,
    pub vault: String, // Reward vault account
}

#[allow(unused)]
/// examples: ["3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv"]
pub async fn get_pool_detail(
    ids: Vec<String>,
) -> Result<PoolDetailResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/pools/key/ids?ids={}", get_url(), ids.join(",")))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[cfg(test)]
mod tests {
    use crate::raydium::pool::{
        get_info_by_ids, get_info_by_lps, get_info_by_mint, get_pool_detail, list_info,
    };

    #[tokio::test]
    async fn test_get_info_by_ids() {
        let _ = tracing_subscriber::fmt::try_init();

        let id = format!("58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2");
        let sol_address = "So11111111111111111111111111111111111111112";
        let usdc_address = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
        let result = get_info_by_ids(vec![
            "58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2".to_string(),
        ])
        .await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);

        let pools = result.data;
        assert!(pools.len() >= 1); // At least one valid pool should be returned

        let info = &pools[0];
        assert!(info.id == id);
        let mint_a = &info.mint_a;
        let mint_b = &info.mint_b;
        assert_eq!(sol_address, mint_a.address);
        assert_eq!(usdc_address, mint_b.address);
    }

    #[tokio::test]
    async fn test_get_info_by_lps() {
        let _ = tracing_subscriber::fmt::try_init();

        let id = format!("58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2");
        let sol_address = "So11111111111111111111111111111111111111112";
        let usdc_address = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";

        let result = get_info_by_lps(vec![
            "8HoQnePLqPj4M7PUDzfw8e3Ymdwgc7NLGnaTUapubyvu".to_string(),
            "72CpTVgh49EsE9p9DCtAUFxteLwgsXbW44gwV2QUauRp".to_string(),
            "A1uUcNmZqTUvYvivBfefzBU8TzKjqeVxmUyjbiaQbBor".to_string(),
            "As3EGgLtUVpdNpE6WCKauyNRrCCwcQ57trWQ3wyRXDa6".to_string(),
        ])
        .await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);

        let pools = result.data;
        assert!(pools.len() == 4); // At least one valid pool should be returned

        let info = &pools[0];
        assert!(info.id == id);
        let mint_a = &info.mint_a;
        let mint_b = &info.mint_b;
        assert_eq!(sol_address, mint_a.address);
        assert_eq!(usdc_address, mint_b.address);
    }

    #[tokio::test]
    async fn test_list_info() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = list_info(
            crate::raydium::pool::PoolType::All,
            crate::raydium::pool::PoolSortField::Liquidity,
            crate::raydium::pool::SortType::Desc,
            1,
            1,
        )
        .await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
        assert!(result.data.data.len() == 1);
        assert!(result.data.has_next_page);
    }

    #[tokio::test]
    async fn test_get_info_by_mint() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = get_info_by_mint(
            format!("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),
            None,
            crate::raydium::pool::PoolType::All,
            crate::raydium::pool::PoolSortField::Liquidity,
            crate::raydium::pool::SortType::Desc,
            100,
            1,
        )
        .await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
        assert!(result.data.data.len() <= 100);
        assert!(result.data.has_next_page);
    }

    #[tokio::test]
    async fn test_get_pool_detail() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = get_pool_detail(vec![
            "3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv".to_string(),
            "AC3SjZfn846bSj2bSGr6SPBnksZjUpeZCmDU8Bv7iWWG".to_string(),
            "BUjQEj81qWNtdzSXnqVyjWtttkhefV4tptVFYpLtpkCn".to_string(),
            "AS5MV3ear4NZPMWXbCsEz3AdbCaXEnq4ChdaWsvLgkcM".to_string(),
            "BGKdk4aKbRMfB6GoZ7gAPFUysuJrspu3k51kc6WFsVsv".to_string(),
            "BZtgQEyS6eXUXicYPHecYQ7PybqodXQMvkjUbP4R8mUU".to_string(),
            "9Z8rZm3w3UGae8LkruR9n1t27rWQcB5JgAdaRksa4eMK".to_string(),
            "2DxoKPcEjZF7FwbpUXnpd8r4ExBtpyGGfLRdm7LiEJGh".to_string(),
            "5XyfteL4v31yugoCZXDQ9EKGRboCDyT38MmLos6R2DF1".to_string(),
            "EWcodkYWdmiEJhum1KMZyA4izJxhaxgp6MXSLEHUkVwX".to_string(),
        ])
        .await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
        assert!(result.data.len() == 10);
        let mint_a = &result.data[0].mint_a;
        let mint_b = &result.data[0].mint_b;
        assert_eq!(
            "So11111111111111111111111111111111111111112",
            mint_a.address
        );
        assert_eq!(
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            mint_b.address
        );
    }
}
