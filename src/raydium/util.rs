use crate::common::config;
use reqwest::Response;
use std::time::Duration;

pub fn get_url() -> String {
    let config = config::get();
    config.get_raydium_url()
}

pub async fn send_get_request(url: String) -> Result<Response, Box<dyn std::error::Error>> {
    let config = config::get();
    let client = reqwest::Client::new();

    let mut last_error = None;
    let max_retry = match config.max_retry {
        0 => 1,
        _ => config.max_retry,
    };

    for _ in 0..=max_retry {
        match client
            .get(&url)
            .header("accept", "application/json")
            .timeout(Duration::from_millis(config.timeout_ms))
            .send()
            .await
        {
            Ok(response) => {
                return Ok(response);
            }
            Err(e) => {
                last_error = Some(e);
                tokio::time::sleep(Duration::from_millis(500)).await;
            }
        }
    }

    Err(Box::new(last_error.unwrap()))
}
