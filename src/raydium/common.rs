// MAIN API
use serde::Deserialize;

use crate::raydium::util::{get_url, send_get_request};

#[derive(Debug, Deserialize)]
#[allow(unused)]
pub struct VersionResponse {
    pub id: String,
    pub success: bool,
    pub data: VersionData,
}

#[derive(Debug, Deserialize)]
#[allow(unused)]
pub struct VersionData {
    pub latest: String,
    pub least: String,
}

#[allow(unused)]
pub async fn version() -> Result<VersionResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/main/version", get_url()))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[derive(Debug, Deserialize)]
#[allow(unused)]
pub struct RpcsResponse {
    pub id: String,
    pub success: bool,
    pub data: RpcData,
}

#[derive(Debug, Deserialize)]
#[allow(unused)]
pub struct RpcData {
    pub strategy: String,
    pub rpcs: Vec<RpcNode>,
}

#[derive(Debug, Deserialize)]
#[allow(unused)]
pub struct RpcNode {
    pub url: String,
    pub batch: bool,
    pub name: String,
    pub weight: u32,
}

#[allow(unused)]
pub async fn rpcs() -> Result<RpcsResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/main/rpcs", get_url()))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[derive(Debug, Deserialize)]
#[allow(unused)]
pub struct ChainTimeResponse {
    pub id: String,
    pub success: bool,
    pub data: ChainTimeData,
}

#[derive(Debug, Deserialize)]
#[allow(unused)]
pub struct ChainTimeData {
    pub offset: i64,
}

#[allow(unused)]
pub async fn chain_time() -> Result<ChainTimeResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/main/chain-time", get_url()))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[allow(unused)]
#[derive(Debug, Deserialize)]
pub struct InfoResponse {
    pub id: String,
    pub success: bool,
    pub data: InfoData,
}

#[allow(unused)]
#[derive(Debug, Deserialize)]
pub struct InfoData {
    pub volume24: f64, // 24小时交易量
    pub tvl: f64,      // 总锁定价值(Total Value Locked)
}

#[allow(unused)]
pub async fn info() -> Result<InfoResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/main/info", get_url()))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[allow(unused)]
#[derive(Debug, Deserialize)]
pub struct AutoFeeResponse {
    pub id: String,
    pub success: bool,
    pub data: FeeTierData,
}

#[allow(unused)]
#[derive(Debug, Deserialize)]
pub struct FeeTierData {
    #[serde(rename = "default")] // 处理关键字冲突
    pub default_tier: FeeTierValues,
}

#[allow(unused)]
#[derive(Debug, Deserialize)]
pub struct FeeTierValues {
    pub vh: u64, // very high 档位
    pub h: u64,  // high 档位
    pub m: u64,  // medium 档位
}

#[allow(unused)]
pub async fn auto_fee() -> Result<AutoFeeResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/main/auto-fee", get_url()))
        .await?
        .json()
        .await?;

    Ok(response)
}

// add some tests
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_version() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = version().await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
    }

    #[tokio::test]
    async fn test_rpcs() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = rpcs().await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
    }

    #[tokio::test]
    async fn test_chain_time() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = chain_time().await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
    }

    #[tokio::test]
    async fn test_info() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = info().await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
    }

    #[tokio::test]
    async fn test_auto_fee() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = auto_fee().await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
    }
}
