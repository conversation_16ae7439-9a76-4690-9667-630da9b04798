use crate::raydium::util::{get_url, send_get_request};
use serde::Deserialize;
use serde::Serialize;
use std::collections::HashMap;

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize)]
pub struct ListResponse {
    pub id: String,
    pub success: bool,
    pub data: ListData,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize)]
pub struct ListData {
    pub blacklist: Vec<String>,
    pub mint_list: Vec<Info>,
    pub white_list: Vec<String>,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize)]
pub struct Info {
    pub chain_id: u32,
    pub address: String,
    pub program_id: String,
    pub logo_uri: String,
    pub symbol: String,
    pub name: String,
    pub decimals: u8,
    pub tags: Vec<String>,

    #[serde(default)]
    pub extensions: MintExtensions,
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize, Default)]
pub struct MintExtensions {
    #[serde(default)]
    pub tips: Option<MintTips>,
    // 其他可能的扩展字段可以添加在这里
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize)]
pub struct MintTips {
    pub text: String,
    pub link: String,
    pub icon: String,
}

#[allow(unused)]
pub async fn list() -> Result<ListResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/mint/list", get_url()))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InfoResponse {
    /// 请求的唯一标识符
    pub id: String,
    /// 请求是否成功
    pub success: bool,
    /// 包含代币信息的数组
    pub data: Vec<TokenInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenInfo {
    /// 区块链ID (101 = Solana主网)
    pub chain_id: u32,
    /// 代币合约地址
    pub address: String,
    /// Token程序ID (通常是SPL Token标准程序)
    pub program_id: String,
    /// 代币图标URL
    #[serde(rename = "logoURI")]
    pub logo_uri: String,
    /// 代币符号 (如"USDC")
    pub symbol: String,
    /// 代币全名 (如"USD Coin")
    pub name: String,
    /// 小数位数 (如6表示1e6为最小单位)
    pub decimals: u8,
    /// 代币标签 (如["hasFreeze"])
    #[serde(default)]
    pub tags: Vec<String>,
    /// 扩展信息 (可能为空对象)
    #[serde(default)]
    pub extensions: TokenExtensions,
}

#[derive(Debug, Serialize, Deserialize, Default)]
#[serde(rename_all = "camelCase")]
pub struct TokenExtensions {
    /// 可选的CoinGecko ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub coingecko_id: Option<String>,
    /// 其他自定义扩展字段
    #[serde(flatten)]
    pub extra: HashMap<String, serde_json::Value>,
}

#[allow(unused)]
pub async fn get_info(ids: Vec<String>) -> Result<InfoResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/mint/ids?mints={}", get_url(), ids.join(",")))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[allow(unused)]
#[derive(Debug, Serialize, Deserialize)]
pub struct PriceResponse {
    pub id: String,
    pub success: bool,
    /// token->usdc, eg: sol_address:180
    pub data: HashMap<String, String>,
}

#[allow(unused)]
// get_price will return price of token in usdc
pub async fn get_price(ids: Vec<String>) -> Result<PriceResponse, Box<dyn std::error::Error>> {
    let response = send_get_request(format!("{}/mint/price?mints={}", get_url(), ids.join(",")))
        .await?
        .json()
        .await?;

    Ok(response)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;
    use tracing_subscriber;

    #[tokio::test]
    async fn test_get_price() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = get_price(vec![format!(
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        )])
        .await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
    }

    #[tokio::test]
    async fn test_get_info() {
        let _ = tracing_subscriber::fmt::try_init();
        let result = get_info(vec![format!(
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        )])
        .await;
        assert!(result.is_ok());
        let result = result.unwrap();
        assert!(result.success);
    }
}
