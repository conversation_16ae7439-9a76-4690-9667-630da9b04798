mod common;
mod database;
mod helius;
mod raydium;
mod strategy;
use crate::common::config::load_config;
use crate::helius::price::get_sol_price_in_usd;
use serde_json::Value;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::instruction::Instruction;
use solana_sdk::native_token::LAMPORTS_PER_SOL;
use solana_sdk::signature::Keypair;
use solana_sdk::signature::Signer;
use solana_sdk::signature::read_keypair_file;
use solana_sdk::transaction::Transaction;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use solana_system_interface::instruction as system_instruction;
use spl_associated_token_account::get_associated_token_address;
use spl_token;
use std::error::Error;
use std::fs;
use std::str::FromStr;
use tracing::{error, info};

fn get_local_keypairs() -> Vec<Keypair> {
    let mut keys = Vec::new();
    let home = std::env::var("HOME").unwrap();
    let solana_dir = format!("{}/.config/solana", home);
    let entries = fs::read_dir(&solana_dir).unwrap();

    for entry in entries {
        let entry = entry.unwrap();
        let path = entry.path();
        if path.extension().map(|s| s == "json").unwrap_or(false) {
            if let Ok(keypair) = read_keypair_file(&path) {
                keys.push(keypair);
            }
        }
    }
    keys
}

#[allow(unused)]
async fn process_jupiter_msg(data: Value) -> Result<(), Box<dyn Error>> {
    if let Some(method) = data.get("method").and_then(|m| m.as_str()) {
        info!("method is {}", method);
        if method == "accountNotification" {
            if let Some(params) = data.get("params") {
                let result = &params["result"];
                let value = &result["value"];

                let lamports = value["lamports"].as_u64().unwrap_or(0);
                let owner = value["owner"].as_str().unwrap_or_default();

                info!("账户余额: {}", lamports);
                info!("账户拥有者: {}", owner);
            }
        }
    }

    Ok(())
}

async fn ensure_account_exist(
    key: &Keypair,
    account: &Pubkey,
    token_mint_address: &Pubkey,
) -> Result<(), Box<dyn std::error::Error>> {
    let config = common::config::get();
    let client =
        RpcClient::new_with_commitment(config.get_rpc_url(), CommitmentConfig::confirmed());
    if let Err(_) = client.get_account(account).await {
        let create_ata_ix =
            spl_associated_token_account::instruction::create_associated_token_account(
                &key.pubkey(),
                &key.pubkey(),
                token_mint_address,
                &spl_token::id(),
            );

        let mut transaction = Transaction::new_with_payer(&[create_ata_ix], Some(&key.pubkey()));
        let recent_blockhash = client.get_latest_blockhash().await?;
        transaction.sign(&[key], recent_blockhash);
        client.send_and_confirm_transaction(&transaction).await?;
    }
    Ok(())
}

async fn swap_wsol_to_goat(
    client: &RpcClient,
    payer: &Keypair,
    wsol_amount: u64,
) -> Result<(), Box<dyn std::error::Error>> {
    let wsol_mint = Pubkey::from_str("So11111111111111111111111111111111111111112")?;
    let goat_mint = Pubkey::from_str("B2qpj8HuB6McyKU8M2EicEEVK5Huc9VpM42sDb7zQAAy")?;
    let pool_id = Pubkey::from_str("12UGE4NtV9tQsYGz6p36hPRkxMiXETmwYhcVRS8wq1oG")?;
    let program_id = Pubkey::from_str("DRaycpLY18LhpbydsBWbVJtxpNv9oXPgjRSfpF2bWpYb")?;

    let user_wsol_account = get_associated_token_address(&payer.pubkey(), &wsol_mint);
    let user_goat_account = get_associated_token_address(&payer.pubkey(), &goat_mint);

    // 确保 GOAT 账户存在
    ensure_account_exist(payer, &user_goat_account, &goat_mint).await?;

    // 获取池子详细信息
    let pool_detail = crate::raydium::pool::get_pool_detail(vec![pool_id.to_string()]).await?;
    let pool = &pool_detail.data[0];

    // 构建 Raydium 交换指令 (这需要 Raydium SDK 或手动构建)
    let swap_instruction = build_raydium_swap_instruction(
        &program_id,
        &pool_id,
        &user_wsol_account,
        &user_goat_account,
        &payer.pubkey(),
        wsol_amount,
        0,             // 最小输出量，实际应该计算
        &pool.vault.a, // WSOL vault
        &pool.vault.b, // GOAT vault
    )?;

    let mut transaction = Transaction::new_with_payer(&[swap_instruction], Some(&payer.pubkey()));
    let recent_blockhash = client.get_latest_blockhash().await?;
    transaction.sign(&[payer], recent_blockhash);
    client.send_and_confirm_transaction(&transaction).await?;

    Ok(())
}

fn build_raydium_swap_instruction(
    program_id: &Pubkey,
    pool_id: &Pubkey,
    user_source: &Pubkey,
    user_destination: &Pubkey,
    user_authority: &Pubkey,
    amount_in: u64,
    minimum_amount_out: u64,
    pool_source: &Pubkey,
    pool_destination: &Pubkey,
) -> Result<Instruction, Box<dyn std::error::Error>> {
    // 这里需要根据 Raydium 的具体指令格式构建
    // 建议使用 Raydium SDK 或参考官方文档
    todo!("需要实现 Raydium 交换指令构建")
}

#[tokio::main(flavor = "multi_thread")]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    let config_path = ".config.toml";
    let config = load_config(config_path)?;
    info!("config is {:#?}", config);

    // Initialize database
    let db_path = config.db_path.clone();
    if let Err(e) = database::common::init_db_with_cfs(&db_path, &[strategy::raydium::CF_NAME]) {
        error!("Failed to initialize database: {}", e);
        return Err(e.into());
    } else {
        info!("Database initialized at: {}", db_path);
    }
    let client =
        RpcClient::new_with_commitment(config.get_rpc_url(), CommitmentConfig::confirmed());

    let price = get_sol_price_in_usd().await?;
    let keys = get_local_keypairs();

    let wsol_mint = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
    let goat_mint = Pubkey::from_str("B2qpj8HuB6McyKU8M2EicEEVK5Huc9VpM42sDb7zQAAy").unwrap();

    //GOAT pool:
    // strategy::raydium::run_triangular_arbitrage_task().await;

    Ok(())
}
